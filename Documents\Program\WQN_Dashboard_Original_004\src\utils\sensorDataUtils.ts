import { parse, format } from 'date-fns';

/**
 * Represents a single sensor reading with timestamp and value
 */
interface SensorReading {
  timestamp: string;
  value: number;
}

/**
 * Represents a row of sensor data from the CSV file
 */
interface CSVReading {
  DateTime: string;
  'Salinity(ppt)': number;
  'DO(mg/L)': number;
  'pH(pH)': number;
  'Temp.(deg.C)': number;
  'Cond.(mS/cm)': number;
  'Depth(m)': number;
  'Turbidity(NTU)': number;
  Latitude: number;
  Longitude: number;
}

/**
 * Dynamically imports sensor readings data for a given sensor ID
 * @param sensorId - ID of the sensor to load readings for
 * @returns Promise resolving to the sensor data or null if import fails
 */
async function importSensorReadings(sensorId: string) {
  try {
    // Try with the original sensor ID first
    try {
      const module = await import(`../components/user/data/sensorReadings/${sensorId}.ts`);
      return module.sensorData;
    } catch (importError) {
      // If the original ID fails, try with mapped equipment ID
      // This handles the case where API IDs differ from equipment IDs
      if (sensorId === '22063') {
        // Map specific API ID to equipment ID based on database mapping
        const equipmentId = 'NB001';
        try {
          const module = await import(`../components/user/data/sensorReadings/${equipmentId}.ts`);
          return module.sensorData;
        } catch (mappedError) {
          console.error(`Failed to import sensor readings with mapped ID ${equipmentId}:`, mappedError);
          return null;
        }
      } else if (sensorId.startsWith('2')) {
        // Generic mapping for other numeric API IDs
        const equipmentId = `NB${sensorId.substring(2).padStart(3, '0')}`;
        try {
          const module = await import(`../components/user/data/sensorReadings/${equipmentId}.ts`);
          return module.sensorData;
        } catch (mappedError) {
          console.error(`Failed to import sensor readings with generic mapped ID ${equipmentId}:`, mappedError);
          return null;
        }
      }

      console.error(`Failed to load sensor readings for ${sensorId}:`, importError);
      return null;
    }
  } catch (error) {
    console.error(`Critical error in importSensorReadings for ${sensorId}:`, error);
    return null;
  }
}

/**
 * Dynamically imports signal readings data for a given sensor ID
 * @param sensorId - ID of the sensor to load signal readings for
 * @returns Promise resolving to the signal data or null if import fails
 */
async function importSignalReadings(sensorId: string) {
  try {
    // Added .ts extension to the dynamic import path
    const module = await import(`../components/user/data/sensorsignalReadings/sr_${sensorId}.ts`);
    return module.sensorsignalData;
  } catch (error) {
    console.error(`Failed to load signal readings for ${sensorId}:`, error);
    return null;
  }
}

// Cache objects to store loaded data
const sensorDataCache: { [key: string]: string } = {};
const signalDataCache: { [key: string]: string } = {};

/**
 * Represents the structure of the latest sensor data from local fallback files.
 */
export interface LatestLocalSensorData {
  salinity: number;
  do: number;
  ph: number;
  temp: number;
  cond: number;
  depth: number;
  turbidity: number;
  latitude: number;
  longitude: number;
  timestamp: string; // Raw timestamp string from CSV dd/MM/yyyy HH:mm
  dateTimeObject: Date; // Parsed Date object for comparison
}

/**
 * Parses the local sensor data CSV and returns the latest entry.
 * @param sensorId - ID of the sensor to get latest local data for.
 * @returns Promise resolving to the latest local sensor data or null if not found/error.
 */
export async function getLatestLocalSensorFallbackData(sensorId: string): Promise<LatestLocalSensorData | null> {
  try {
    const csvDataString = await getSensorData(sensorId);
    if (!csvDataString) {
      console.warn(`No local fallback data string found for sensor ${sensorId}`);
      return null;
    }

    const lines = csvDataString.trim().split('\n');
    if (lines.length <= 1) {
      console.warn(`Local fallback data for ${sensorId} is empty or has no data rows.`);
      return null;
    }

    // Skip header, take the first data line (which is the latest as per file structure)
    const latestLine = lines[1];
    const values = latestLine.split('\t');

    if (values.length < 10) {
      console.error(`Malformed latest data line in local fallback for ${sensorId}: ${latestLine}`);
      return null;
    }
    
    const rawTimestamp = values[0];
    const dateTimeObject = parse(rawTimestamp, 'dd/MM/yyyy HH:mm', new Date());

    if (isNaN(dateTimeObject.getTime())) {
        console.error(`Invalid date format in local fallback data for ${sensorId}: ${rawTimestamp}`);
        return null;
    }

    return {
      salinity: parseFloat(values[1]) || 0,
      do: parseFloat(values[2]) || 0,
      ph: parseFloat(values[3]) || 0,
      temp: parseFloat(values[4]) || 0,
      cond: parseFloat(values[5]) || 0,
      depth: parseFloat(values[6]) || 0,
      turbidity: parseFloat(values[7]) || 0,
      latitude: parseFloat(values[8]) || 0,
      longitude: parseFloat(values[9]) || 0,
      timestamp: rawTimestamp, // Store the original dd/MM/yyyy HH:mm string
      dateTimeObject: dateTimeObject
    };
  } catch (error) {
    console.error(`Error getting latest local fallback data for sensor ${sensorId}:`, error);
    return null;
  }
}


/**
 * Gets sensor data with caching for better performance
 * @param sensorId - ID of the sensor to get data for
 * @returns Promise resolving to the sensor data or null if not found
 */
export async function getSensorData(sensorId: string): Promise<string | null> {
  if (sensorDataCache[sensorId]) {
    return sensorDataCache[sensorId];
  }

  const data = await importSensorReadings(sensorId);
  if (data) {
    sensorDataCache[sensorId] = data;
  }
  return data;
}

/**
 * Gets signal data with caching for better performance
 * @param sensorId - ID of the sensor to get signal data for
 * @returns Promise resolving to the signal data or null if not found
 */
export async function getSignalData(sensorId: string): Promise<string | null> {
  if (signalDataCache[sensorId]) {
    return signalDataCache[sensorId];
  }

  const data = await importSignalReadings(sensorId);
  if (data) {
    signalDataCache[sensorId] = data;
  }
  return data;
}

/**
 * Parses raw CSV data into structured sensor readings
 * @param sensorId - ID of the sensor to parse data for
 * @returns Promise resolving to array of parsed CSV readings
 */
async function parseCSVData(sensorId: string): Promise<CSVReading[]> {
  try {
    const csvData = await getSensorData(sensorId);
    if (!csvData) {
      throw new Error(`No data found for sensor ${sensorId}`);
    }

    const lines = csvData.split('\n');
    const headers = lines[0].split('\t');
    
    return lines.slice(1).map(line => {
      const values = line.split('\t');
      return {
        DateTime: values[0],
        'Salinity(ppt)': parseFloat(values[1]),
        'DO(mg/L)': parseFloat(values[2]),
        'pH(pH)': parseFloat(values[3]),
        'Temp.(deg.C)': parseFloat(values[4]),
        'Cond.(mS/cm)': parseFloat(values[5]),
        'Depth(m)': parseFloat(values[6]),
        'Turbidity(NTU)': parseFloat(values[7]),
        Latitude: parseFloat(values[8]),
        Longitude: parseFloat(values[9])
      };
    });
  } catch (error) {
    console.error(`Error reading CSV for sensor ${sensorId}:`, error);
    return [];
  }
}

/**
 * Represents a signal reading from a sensor
 */
export interface SignalReading {
  signalLevel: number;
  status: 'online' | 'offline' | 'unknown';
  latitude: number;
  longitude: number;
}

/**
 * Gets readings for a specific sensor parameter
 * @param sensorId - ID of the sensor to get readings for
 * @param parameter - Parameter to get readings for (e.g. 'salinity', 'temp')
 * @returns Promise resolving to array of sensor readings
 */
export async function getSensorReadings(sensorId: string | undefined, parameter: string): Promise<SensorReading[]> {
  if (!sensorId) return [];

  try {
    const csvData = await parseCSVData(sensorId);
    const parameterMap: { [key: string]: keyof CSVReading } = {
      salinity: 'Salinity(ppt)',
      do: 'DO(mg/L)',
      ph: 'pH(pH)',
      temp: 'Temp.(deg.C)',
      cond: 'Cond.(mS/cm)',
      depth: 'Depth(m)',
      turbidity: 'Turbidity(NTU)'
    };

    return csvData.map(row => ({
      timestamp: format(parse(row.DateTime, 'dd/MM/yyyy HH:mm', new Date()), 'yyyy-MM-dd HH:mm:ss'),
      // Explicitly cast value to number, as parameterMap ensures it maps to a numeric CSVReading key
      value: row[parameterMap[parameter]] as number
    }));
  } catch (error) {
    console.error(`Error getting readings for sensor ${sensorId}:`, error);
    return [];
  }
}

/**
 * Gets the latest sensor data including all parameters and location
 * @param sensorId - ID of the sensor to get latest data for
 * @returns Promise resolving to latest sensor data or null if not found
 */
export async function getLatestSensorData(sensorId: string | undefined): Promise<{
  readings: {
    salinity: number;
    do: number;
    ph: number;
    temp: number;
    cond: number;
    depth: number;
    turbidity: number;
  };
  location: {
    latitude: number;
    longitude: number;
  };
  timestamp: string;
} | null> {
  if (!sensorId) return null;

  try {
    const csvData = await parseCSVData(sensorId);
    if (csvData.length === 0) return null;

    const latestReading = csvData[0];
    const timestamp = latestReading.DateTime;
    
    // Add AM/PM to timestamp if not already present
    const formattedTimestamp = timestamp.includes('AM') || timestamp.includes('PM') 
      ? timestamp 
      : (() => {
          const [date, time] = timestamp.split(' ');
          const [hours] = time.split(':');
          const ampm = parseInt(hours) >= 12 ? 'PM' : 'AM';
          return `${timestamp} ${ampm}`;
        })();
    
    return {
      readings: {
        salinity: latestReading['Salinity(ppt)'],
        do: latestReading['DO(mg/L)'],
        ph: latestReading['pH(pH)'],
        temp: latestReading['Temp.(deg.C)'],
        cond: latestReading['Cond.(mS/cm)'],
        depth: latestReading['Depth(m)'],
        turbidity: latestReading['Turbidity(NTU)']
      },
      location: {
        latitude: latestReading.Latitude,
        longitude: latestReading.Longitude
      },
      timestamp: formattedTimestamp
    };
  } catch (error) {
    console.error(`Error getting latest data for sensor ${sensorId}:`, error);
    return null;
  }
}

/**
 * Exports sensor readings within a date range as CSV
 * @param sensorId - ID of the sensor to export data for
 * @param startDate - Start date for filtering readings
 * @param endDate - End date for filtering readings
 * @param parameters - Array of parameters to include in export
 * @returns Promise resolving to CSV string of filtered readings
 */
export async function exportSensorReadings(
  sensorId: string,
  startDate: Date,
  endDate: Date,
  parameters: string[]
): Promise<string> {
  try {
    const csvData = await parseCSVData(sensorId);
    const parameterMap: { [key: string]: keyof CSVReading } = {
      salinity: 'Salinity(ppt)',
      do: 'DO(mg/L)',
      ph: 'pH(pH)',
      temp: 'Temp.(deg.C)',
      cond: 'Cond.(mS/cm)',
      depth: 'Depth(m)',
      turbidity: 'Turbidity(NTU)'
    };

    const filteredData = csvData.filter(row => {
      const rowDate = parse(row.DateTime, 'dd/MM/yyyy HH:mm', new Date());
      return rowDate >= startDate && rowDate <= endDate;
    });

    const headers = ['DateTime', ...parameters.map(p => parameterMap[p]), 'Latitude', 'Longitude'];
    const rows = filteredData.map(row => {
      return [
        row.DateTime,
        ...parameters.map(p => row[parameterMap[p]]),
        row.Latitude,
        row.Longitude
      ].join(',');
    });

    return [headers.join(','), ...rows].join('\n');
  } catch (error) {
    console.error(`Error exporting data for sensor ${sensorId}:`, error);
    return '';
  }
}

/**
 * Gets the latest signal data for a sensor
 * @param sensorId - ID of the sensor to get signal data for
 * @returns Promise resolving to signal reading or null if not found
 */
export async function getSensorSignalData(sensorId: string | undefined): Promise<SignalReading | null> {
  if (!sensorId) return null;

  try {
    const signalData = await getSignalData(sensorId);
    if (!signalData) return null;

    const lines = signalData.split('\n');
    const values = lines[1].split('\t');
    
    return {
      signalLevel: parseInt(values[0], 10),
      status: values[1] as 'online' | 'offline' | 'unknown',
      latitude: parseFloat(values[2]),
      longitude: parseFloat(values[3])
    };
  } catch (error) {
    console.error(`Error getting signal data for sensor ${sensorId}:`, error);
    return null;
  }
}
