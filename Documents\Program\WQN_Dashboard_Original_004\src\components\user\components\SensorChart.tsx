import React from 'react';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  TimeScale
} from 'chart.js';
import 'chartjs-adapter-date-fns';
import { TimeFrame } from './TimeFrameToggle';

// Register the necessary components for Chart.js
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  TimeScale
);

// Define the props for the SensorChart component
interface SensorChartProps {
  data: Array<{ timestamp: string; value: number }>; // Array of sensor readings with timestamps
  title: string; // Title of the chart
  unit: string; // Unit of measurement for the sensor data
  color: string; // Color for the chart line
  timeFrame: TimeFrame; // Time frame for the data display
  isLoading?: boolean; // Loading state for the chart
}

// SensorChart component for displaying sensor data in a line chart
const SensorChart: React.FC<SensorChartProps> = ({ data, title, unit, color, timeFrame, isLoading = false }) => {
  // Handle empty data case
  if (!data || data.length === 0) {
    return (
      <div style={{ height: '300px' }} className="flex items-center justify-center">
        <div className="text-center">
          <div className="text-gray-500 mb-2">No data available</div>
          <div className="text-sm text-gray-400">
            {isLoading ? 'Loading chart data...' : `No ${title.toLowerCase()} readings found`}
          </div>
        </div>
      </div>
    );
  }

  // Find the latest reading from the data (matching docs/SensorChart.tsx exactly)
  const latestReading = data.reduce((latest, current) => {
    return new Date(current.timestamp) > new Date(latest.timestamp) ? current : latest;
  }, data[0]);

  // Get the timestamp of the latest reading
  const latestTimestamp = new Date(latestReading.timestamp);
  // Determine the time frame in days based on the selected time frame
  const timeFrameInDays = timeFrame === '1d' ? 1 : timeFrame === '7d' ? 7 : 30;
  // Calculate the start time for filtering data
  const startTime = new Date(latestTimestamp.getTime() - (timeFrameInDays * 24 * 60 * 60 * 1000));

  // Filter the data to include only readings within the selected time frame
  const filteredData = data.filter(reading => {
    const readingDate = new Date(reading.timestamp);
    return readingDate >= startTime && readingDate <= latestTimestamp;
  });

  // Prepare the data structure for the chart (matching docs/SensorChart.tsx exactly)
  const chartData = {
    datasets: [
      {
        label: `${title} (${unit})`, // Label for the dataset
        data: filteredData.map(reading => {
          // Adjust timestamp by reducing 4 hours (timezone adjustment)
          const originalTime = new Date(reading.timestamp);
          const adjustedTime = new Date(originalTime.getTime() - (4 * 60 * 60 * 1000));
          return {
            x: adjustedTime, // X-axis value (timestamp with 4-hour adjustment)
            y: reading.value // Y-axis value (sensor reading)
          };
        }),
        borderColor: color, // Line color
        backgroundColor: color, // Background color for the line
        tension: 0.4, // Tension for the line curve
        pointRadius: 0, // Radius of points on the line
        borderWidth: 2 // Width of the line
      }
    ]
  };

  // Chart options configuration (matching docs/SensorChart.tsx exactly)
  const options = {
    responsive: true, // Make the chart responsive
    maintainAspectRatio: false, // Allow the chart to change aspect ratio
    plugins: {
      legend: {
        position: 'top' as const // Position of the legend
      },
      title: {
        display: true, // Display the title
        text: `${title} - Last ${timeFrameInDays} Days` // Title text
      }
    },
    scales: {
      x: {
        type: 'time' as const, // X-axis type is time
        time: {
          unit: timeFrame === '1d' ? 'hour' : timeFrame === '7d' ? 'day' : 'week' as const, // Time unit based on selected time frame
          displayFormats: {
            hour: 'dd/MM HH:mm', // Format for hour display
            day: 'dd/MM', // Format for day display
            week: 'dd/MM' // Format for week display
          }
        },
        title: {
          display: true, // Display the title for the X-axis
          text: 'Time' // Title text for the X-axis
        },
        min: new Date(startTime.getTime() - (4 * 60 * 60 * 1000)).toISOString(), // Minimum value for the X-axis (adjusted)
        max: new Date(latestTimestamp.getTime() - (4 * 60 * 60 * 1000)).toISOString() // Maximum value for the X-axis (adjusted)
      },
      y: {
        beginAtZero: false, // Do not start Y-axis at zero
        title: {
          display: true, // Display the title for the Y-axis
          text: unit // Title text for the Y-axis
        }
      }
    }
  };

  // Render the chart component (matching docs/SensorChart.tsx exactly)
  return (
    <div style={{ height: '300px' }}>
      <Line data={chartData} options={options} /> {/* Render the Line chart with data and options */}
    </div>
  );
};

export default SensorChart; // Export the SensorChart component